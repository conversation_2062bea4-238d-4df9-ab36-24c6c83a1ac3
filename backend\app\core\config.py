"""
應用程序配置設置
"""

from typing import List, Optional, Union
from pydantic import AnyHttpUrl, field_validator
from pydantic_settings import BaseSettings
from pathlib import Path
import os


class Settings(BaseSettings):
    """應用程序設置"""
    
    # 基本設置
    PROJECT_NAME: str = "購案審查系統"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # 服務器設置
    HOST: str = "0.0.0.0"
    PORT: int = 8001
    DEBUG: bool = False
    DISABLE_RELOAD: bool = False
    
    # 安全設置
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 天
    
    # CORS 設置
    ALLOWED_HOSTS: List[str] = ["*"]
    
    @field_validator("ALLOWED_HOSTS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # 數據庫設置
    DATABASE_URL: str = "sqlite:///./purchase_review.db"
    DATABASE_TYPE: str = "sqlite"  # sqlite, postgresql, mysql

    # PostgreSQL 設置
    POSTGRES_HOST: str = "localhost"
    POSTGRES_PORT: int = 5432
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "password"
    POSTGRES_DB: str = "purchase_review"

    # MySQL 設置
    MYSQL_HOST: str = "localhost"
    MYSQL_PORT: int = 3306
    MYSQL_USER: str = "root"
    MYSQL_PASSWORD: str = "password"
    MYSQL_DB: str = "purchase_review"

    # 資料庫連接池設置
    DB_POOL_SIZE: int = 10
    DB_MAX_OVERFLOW: int = 20
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600  # 1小時

    # 資料庫連接重試設置
    DB_RETRY_ATTEMPTS: int = 3
    DB_RETRY_DELAY: int = 1
    
    # Redis 設置 (用於 Celery)
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # 文件存儲設置
    UPLOAD_DIR: str = "./uploads"
    TEMP_DIR: str = "./temp"
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_FILE_TYPES: List[str] = [".pdf", ".doc", ".docx", ".odt", ".ods", ".odp", ".odg", ".odf"]
    
    # PDF 解析設置
    PDF_PARSE_TIMEOUT: int = 300  # 5 分鐘
    OCR_LANGUAGE: str = "chi_tra+eng"  # 繁體中文 + 英文
    
    # AI 模型設置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-4-vision-preview"
    OPENAI_MAX_TOKENS: int = 4000

    # Ollama 本地模型設置
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_MODEL: str = "mistral_small_3_1_2503"
    OLLAMA_TIMEOUT: int = 300  # 5 分鐘
    OLLAMA_MAX_TOKENS: int = 4000
    OLLAMA_TEMPERATURE: float = 0.1
    
    # GraphRAG 設置
    GRAPHRAG_ENABLED: bool = True
    GRAPHRAG_MODEL_PATH: str = "./models/graphrag"
    
    # 日誌設置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "./logs/app.log"
    
    # Celery 設置
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    
    # 監控設置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    
    def get_database_url(self) -> str:
        """根據配置動態構建資料庫URL"""
        if self.DATABASE_TYPE == "postgresql":
            return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
        elif self.DATABASE_TYPE == "mysql":
            return f"mysql+pymysql://{self.MYSQL_USER}:{self.MYSQL_PASSWORD}@{self.MYSQL_HOST}:{self.MYSQL_PORT}/{self.MYSQL_DB}"
        else:
            # 默認使用 SQLite
            return self.DATABASE_URL

    def get_database_config(self) -> dict:
        """獲取資料庫配置參數"""
        base_config = {
            "echo": False,  # 禁用SQL查詢日誌輸出
            "pool_pre_ping": True,
        }

        if self.DATABASE_TYPE == "sqlite":
            base_config.update({
                "connect_args": {
                    "check_same_thread": False,
                    "timeout": 20
                },
                "poolclass": "StaticPool"
            })
        else:
            base_config.update({
                "pool_size": self.DB_POOL_SIZE,
                "max_overflow": self.DB_MAX_OVERFLOW,
                "pool_timeout": self.DB_POOL_TIMEOUT,
                "pool_recycle": self.DB_POOL_RECYCLE,
            })

        return base_config

    class Config:
        env_file = ".env"
        case_sensitive = True


# 創建設置實例
settings = Settings()


def get_database_url() -> str:
    """獲取數據庫連接 URL"""
    return settings.DATABASE_URL


def get_upload_path() -> Path:
    """獲取上傳目錄路徑"""
    path = Path(settings.UPLOAD_DIR)
    path.mkdir(parents=True, exist_ok=True)
    return path


def get_temp_path() -> Path:
    """獲取臨時目錄路徑"""
    path = Path(settings.TEMP_DIR)
    path.mkdir(parents=True, exist_ok=True)
    return path


def is_file_allowed(filename: str) -> bool:
    """檢查文件類型是否允許"""
    file_ext = Path(filename).suffix.lower()
    return file_ext in settings.ALLOWED_FILE_TYPES


def get_log_config() -> dict:
    """獲取日誌配置"""
    return {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            },
            "detailed": {
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(module)s - %(funcName)s - %(message)s",
            },
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "default",
                "stream": "ext://sys.stdout",
            },
            "file": {
                "class": "logging.handlers.RotatingFileHandler",
                "level": settings.LOG_LEVEL,
                "formatter": "detailed",
                "filename": settings.LOG_FILE,
                "maxBytes": 10485760,  # 10MB
                "backupCount": 5,
                "encoding": "utf-8",
            },
        },
        "loggers": {
            "": {
                "level": settings.LOG_LEVEL,
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "uvicorn": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False,
            },
            "uvicorn.error": {
                "level": "INFO",
                "handlers": ["console", "file"],
                "propagate": False,
            },
            "uvicorn.access": {
                "level": "INFO",
                "handlers": ["console"],
                "propagate": False,
            },
            # 禁用SQLAlchemy相關的SQL查詢日誌
            "sqlalchemy.engine": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False,
            },
            "sqlalchemy.dialects": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False,
            },
            "sqlalchemy.pool": {
                "level": "WARNING",
                "handlers": ["file"],
                "propagate": False,
            },
        },
    }
